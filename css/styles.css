body {
  font-family: <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, sans-serif;
  background: radial-gradient(1200px 600px at 20% 0%, #0f2027 0%, #121212 60%, #0b0b0b 100%);
  color: #eee;
  margin: 0;
  padding: 1rem;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

h1 {
  text-align: center;
  margin-bottom: 1rem;
  color: #4FC3F7;
  letter-spacing: 0.5px;
  text-shadow: 0 1px 2px rgba(0,0,0,0.4);
}

#controls {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

#searchBox, #sortSelect, #sortDirection {
  padding: 0.5rem 1rem;
  font-size: 1rem;
  border-radius: 5px;
  border: none;
}

#folderFilter { /* legacy id if any remains */
  background: #2a2a2a;
  color: #eee;
  min-width: 200px;
}

.controls-row { display: flex; gap: 1rem; flex-wrap: wrap; justify-content: center; margin-bottom: 0.75rem; }
.folder-chips { display: flex; gap: 0.5rem; flex-wrap: wrap; justify-content: center; }
.chip {
  display: inline-flex;
  align-items: center;
  gap: 0.4rem;
  background: linear-gradient(180deg, #1b1b1b, #161616);
  border: 1px solid #2b2b2b;
  color: #ddd;
  padding: 0.35rem 0.7rem;
  border-radius: 999px;
  cursor: pointer;
  user-select: none;
  transition: all 0.15s ease, transform 0.1s ease;
  font-size: 0.9rem;
  box-shadow: 0 1px 0 rgba(255,255,255,0.03) inset, 0 1px 4px rgba(0,0,0,0.3);
}
.chip:active { transform: translateY(1px); }
.chip input { appearance: none; width: 14px; height: 14px; border: 2px solid #666; border-radius: 3px; display: inline-block; position: relative; background: #111; }
.chip input:checked { background: #4FC3F7; border-color: #4FC3F7; box-shadow: 0 0 0 2px rgba(79,195,247,0.25); }
.chip:hover { border-color: #4FC3F7; background: #1f1f1f; }

.stats-bar {
  text-align: center;
  margin-bottom: 1rem;
  color: #999;
  font-size: 0.9rem;
}

.grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1rem;
}

.card {
  background: #1e1e1e;
  padding: 1rem;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 10px 20px rgba(0,0,0,0.35), 0 1px 0 rgba(255,255,255,0.04) inset;
  position: relative;
  border: 1px solid #2a2a2a;
  transition: transform 0.12s ease, box-shadow 0.2s ease, border-color 0.2s ease;
}
.card:hover { transform: translateY(-2px); box-shadow: 0 14px 30px rgba(0,0,0,0.45); border-color: #333; }
.card .folder-badge {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: linear-gradient(180deg, #4FC3F7, #28b6f6);
  color: #001018;
  padding: 0.2rem 0.5rem;
  border-radius: 15px;
  font-size: 0.7rem;
  font-weight: bold;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  box-shadow: 0 2px 6px rgba(40,182,246,0.4);
}
.card .refresh-db {
  position: absolute;
  top: 2.4rem; /* just under the folder badge */
  right: 0.5rem;
  background: linear-gradient(180deg, rgba(40,40,40,0.96), rgba(26,26,26,0.96));
  color: #eee;
  border: 1px solid #3a3a3a;
  border-radius: 14px;
  padding: 0.3rem 0.4rem;
  width: 34px;
  height: 30px;
  cursor: pointer;
  transition: all 0.15s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  z-index: 5;
  box-shadow: 0 2px 8px rgba(0,0,0,0.45), 0 0 0 3px rgba(79,195,247,0);
}
.card .refresh-db:hover { border-color: #4FC3F7; color: #4FC3F7; box-shadow: 0 2px 10px rgba(0,0,0,0.5), 0 0 0 3px rgba(79,195,247,0.25); }
.card .refresh-db:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
.card .refresh-db svg { width: 18px; height: 18px; display: block; filter: drop-shadow(0 1px 1px rgba(0,0,0,0.4)); }

@keyframes spin { from { transform: rotate(0);} to { transform: rotate(360deg);} }
.card .refresh-db .spin { animation: spin 800ms linear infinite; }
.card h3 {
  margin: 0.5rem 0;
  font-size: 1.1rem;
  color: #81D4FA;
  padding-right: 130px; /* Make room for folder badge */
}
.card a { text-decoration: none; color: #4FC3F7; }
.card a:hover { text-decoration: underline; }

.card .model-link {
  display: block;
  color: #ffa726;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
  cursor: pointer;
  user-select: none;
  transition: color 0.15s ease;
}
.card .model-link:hover { color: #ffb74d; }

.card video, .card img {
  width: 100%;
  max-height: 200px;
  object-fit: cover;
  border-radius: 8px;
  margin-bottom: 0.5rem;
}

.card p { 
	font-size: 0.85rem;
	color: #ccc;
	display: -webkit-box;
	-webkit-line-clamp: 3;
	-webkit-box-orient: vertical;
	overflow: hidden;
	border-style: dashed;
	border-radius: 50px;
    padding: 0.6em 1em;
    cursor: pointer;
}

.hidden { display: none !important; }

/* Popup */
.popup {
  position: fixed;
  top: 0; left: 0; width: 100%; height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: none;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(2px);
}
.popup-content {
  background: #222;
  background: linear-gradient(180deg, #222, #1b1b1b);
  padding: 1rem 1.25rem 1.25rem;
  border-radius: 14px;
  max-width: 900px;
  max-height: 90vh;
  overflow: auto;
  border: 1px solid #2a2a2a;
  box-shadow: 0 20px 60px rgba(0,0,0,0.6), 0 1px 0 rgba(255,255,255,0.05) inset;
  animation: popIn 160ms ease-out;
}
@keyframes popIn { from { transform: translateY(8px) scale(0.98); opacity: 0; } to { transform: translateY(0) scale(1); opacity: 1; } }
.popup-header { position: sticky; top: 0; background: linear-gradient(180deg, rgba(30,30,30,0.95), rgba(27,27,27,0.9)); padding: 0.75rem 0.25rem 0.75rem 0; display: grid; grid-template-columns: 1fr auto; align-items: center; gap: 0.5rem; z-index: 2; backdrop-filter: blur(3px); }
.popup-header h2 { margin: 0; color: #4FC3F7; display: flex; align-items: center; gap: 0.5rem; }
.popup-header .icon { filter: drop-shadow(0 1px 2px rgba(0,0,0,0.4)); }
.btn-icon { background: #2a2a2a; border: 1px solid #3a3a3a; color: #eee; width: 32px; height: 32px; border-radius: 8px; display: inline-flex; align-items: center; justify-content: center; cursor: pointer; transition: all 0.15s ease; }
.btn-icon:hover { background: #323232; border-color: #4FC3F7; color: #4FC3F7; box-shadow: 0 0 0 3px rgba(79,195,247,0.2); }
.btn-icon:active { transform: translateY(1px); }
.btn-close { font-size: 18px; line-height: 1; }
.popup-sub { display: flex; gap: 1rem; flex-wrap: wrap; margin-top: 0.5rem; opacity: 0.9; }
.meta-item { background: #1a1a1a; border: 1px solid #2a2a2a; padding: 0.25rem 0.5rem; border-radius: 6px; }
.meta-label { color: #9ecff1; margin-right: 0.35rem; }
.meta-value { color: #ddd; }
.divider { height: 1px; background: linear-gradient(90deg, rgba(79,195,247,0), rgba(79,195,247,0.35), rgba(79,195,247,0)); margin: 0.75rem 0 1rem; }
.popup-content h2 { color: #4FC3F7; margin-top: 0; }
.popup-content video, .popup-content img { max-width: 100%; max-height: 300px; margin: 0.5rem 0; display: block; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.4); }

.media-scroll { display: flex; overflow-x: auto; gap: 1rem; padding: 0.5rem 0; scroll-behavior: smooth; }
.media-scroll img, .media-scroll video { flex: 0 0 auto; height: 200px; max-width: 100%; border-radius: 8px; }

pre { font-family: Consolas, monospace; font-size: 0.9rem; background: #1e1e1e; }

/* Loading fallback styles */
.media-error { height: 200px; display: flex; align-items: center; justify-content: center; background: #333; color: #999; border-radius: 8px; margin-bottom: 0.5rem; }
