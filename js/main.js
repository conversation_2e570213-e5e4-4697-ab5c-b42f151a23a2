(function(){
  'use strict';

  // Elements
  const galleryEl = document.getElementById('gallery');
  const searchBox = document.getElementById('searchBox');
  const partialMatch = document.getElementById('partialMatch');
  const includeTxt = document.getElementById('includeTxt');
  const folderChips = document.getElementById('folderChips');
  const sortSelect = document.getElementById('sortSelect');
  const sortDirection = document.getElementById('sortDirection');
  const statsBar = document.getElementById('statsBar');
  const popup = document.getElementById('popup');
  const popupBody = document.getElementById('popupBody');
  const sentinel = document.getElementById('sentinel');
  let refreshing = false;

  // State
  let models = [];
  let allFolders = [];
  let currentList = [];
  let renderedCount = 0;
  const PAGE_SIZE = 40;
  let loading = false;
  let done = false;

  // Templates
  let itemTemplate = '';
  let popupTemplate = '';

  // Utils
  const debounce = (fn, ms=200) => {
    let t; return (...args) => { clearTimeout(t); t = setTimeout(() => fn.apply(null, args), ms); };
  };

  function normalizeText(str) {
    return (str || '')
      .toLowerCase()
      .replace(/['"\`]/g, "'")
      .replace(/[^a-z0-9\s']/gi, ' ')
      .replace(/\s+/g, ' ')
      .trim();
  }

  function escapeRegExp(str) {
    return str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  function buildWordRegex(term) {
    // Match whole word on normalized text (space-delimited)
    const t = escapeRegExp(term);
    return new RegExp(`(^| )${t}( |$)`);
  }

  async function fetchText(path) {
    const res = await fetch(path);
    if (!res.ok) throw new Error(`${path} -> ${res.status}`);
    return await res.text();
  }

  async function loadJSON(path) {
    try {
      const response = await fetch(path);
      if (!response.ok) throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      return await response.json();
    } catch (err) {
      statsBar.textContent = `Error loading data: ${err.message}`;
      return [];
    }
  }

  function populateFolderFilter(data, preselected = []) {
    const folders = [...new Set(data.map(entry => entry.folder_path || 'Unknown'))].sort();
    allFolders = folders;
    folderChips.innerHTML = '';

    const frag = document.createDocumentFragment();
    folders.forEach(folder => {
      const id = `chip_${folder.replace(/[^a-zA-Z0-9_-]/g,'_')}`;
      const label = document.createElement('label');
      label.className = 'chip';
      label.setAttribute('for', id);
      const input = document.createElement('input');
      input.type = 'checkbox';
      input.id = id;
      input.value = folder;
      if (preselected.includes(folder)) input.checked = true;
      const span = document.createElement('span');
      span.textContent = folder;
      label.appendChild(input);
      label.appendChild(span);
      frag.appendChild(label);
    });
    folderChips.appendChild(frag);
  }

  function getSelectedFolders() {
    const checked = folderChips.querySelectorAll('input[type="checkbox"]:checked');
    return Array.from(checked).map(i => i.value);
  }

  function constructMediaPath(folderPath, fileName) {
    const cleanFolder = folderPath || '';
    const cleanFile = fileName || '';
    if (!cleanFile) return '';
    const fullPath = cleanFolder ? `loras/${cleanFolder}/${cleanFile}` : `loras/${cleanFile}`;
    const pathParts = fullPath.split('/');
    return pathParts.map(part => encodeURIComponent(part)).join('/');
  }

  function getPreviewFile(entry) {
    const files = (entry.video_image_files || '').split(',').map(f => f.trim()).filter(f => f);
    if (files.length === 0) return '';
    const baseName = entry.model?.replace(/\.[^/.]+$/, '') || '';
    const folderPath = entry.folder_path || '';
    const exactMatch = files.find(f => {
      const filename = f.toLowerCase();
      const bn = baseName.toLowerCase();
      return filename === `${bn}.mp4` || filename === `${bn}.jpg` || filename === `${bn}.jpeg` || filename === `${bn}.png` || filename === `${bn}.webp`;
    });
    const selectedFile = exactMatch || files[0];
    return constructMediaPath(folderPath, selectedFile);
  }

  function buildMediaTag(entry) {
    const mediaFile = getPreviewFile(entry);
    const ext = mediaFile ? mediaFile.split('.').pop().toLowerCase() : '';
    if (mediaFile && ['mp4', 'webm'].includes(ext)) {
      return `\n<video src="${mediaFile}" muted loop preload="metadata" onmouseenter="this.play().catch(()=>{})" onmouseleave="this.pause();this.currentTime=0;" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex'"></video>\n<div class="media-error" style="display:none">Video failed to load</div>`;
    } else if (mediaFile && ['jpg', 'jpeg', 'png', 'webp'].includes(ext)) {
      return `\n<img src="${mediaFile}" alt="${entry.name}" loading="lazy" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex'">\n<div class="media-error" style="display:none">Image failed to load</div>`;
    }
    return `<div class="media-error">No preview available</div>`;
  }

  function escapeHtml(str) {
    return (str || '').replace(/[&<>"']/g, s => ({'&':'&amp;','<':'&lt;','>':'&gt;','"':'&quot;','\'':'&#39;'}[s]));
  }

  function createCard(entry) {
    const folderName = entry.folder_path || 'Unknown';
    const plainTextDesc = (entry.description || '').replace(/<[^>]+>/g, '').trim();
    const mediaTag = buildMediaTag(entry);

    let html = itemTemplate
      .replaceAll('{{FOLDER_NAME}}', escapeHtml(folderName))
      .replaceAll('{{MEDIA_TAG}}', mediaTag)
      .replaceAll('{{NAME}}', escapeHtml(entry.name))
      .replaceAll('{{URL}}', entry.url ? escapeHtml(entry.url) : '#')
      .replaceAll('{{MODEL}}', escapeHtml(entry.model))
      .replaceAll('{{PLAIN_DESC}}', escapeHtml(plainTextDesc || 'No description'));

    const wrapper = document.createElement('div');
    wrapper.innerHTML = html.trim();
    const card = wrapper.firstElementChild;
    card.dataset.name = normalizeText(entry.name);
    card.dataset.description = normalizeText(plainTextDesc);
    card.dataset.folder = entry.folder_path || '';
    card.dataset.model = entry.model || '';

    // Attach popup open handler without inlining JSON into HTML
    const openBtn = card.querySelector('.open-popup');
    if (openBtn) {
      openBtn.addEventListener('click', () => showPopup(entry));
    }
    return card;
  }

  function renderNextBatch() {
    if (loading || done) return;
    loading = true;
    const start = renderedCount;
    const end = Math.min(renderedCount + PAGE_SIZE, currentList.length);
    const frag = document.createDocumentFragment();
    for (let i = start; i < end; i++) frag.appendChild(createCard(currentList[i]));
    galleryEl.appendChild(frag);
    renderedCount = end;
    if (renderedCount >= currentList.length) done = true;
    loading = false;
    updateStats();
  }

  function resetAndRender() {
    galleryEl.innerHTML = '';
    renderedCount = 0;
    done = false;
    renderNextBatch();
  }

  async function refreshDatabase(clickedBtn){
    if (refreshing) return;
    refreshing = true;
    const btn = clickedBtn || null;
    const svg = btn ? btn.querySelector('svg') : null;
    if (btn) { btn.disabled = true; if (svg) svg.classList.add('spin'); }
    const started = Date.now();
    statsBar.textContent = 'Refreshing database...';
    try {
      // Determine targeted item from the clicked card
      let payload = {};
      if (btn) {
        const card = btn.closest('.card');
        console.log('[Refresh] Button clicked:', btn);
        console.log('[Refresh] Resolved card:', card);
        if (card) {
          const folder_path = card.dataset.folder || '';
          const model = card.dataset.model || '';
          console.log('[Refresh] Card dataset:', { folder_path, model });
          if (folder_path && model) {
            payload = { folder_path, model };
          }
        }
      }

      const resp = await fetch('/api/refresh', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      });
      if (!resp.ok) throw new Error('HTTP ' + resp.status);
      // Even if server returns an object, we don't strictly need the body here
      const data = await resp.json().catch(()=>({}));
      console.log('[Refresh] Server response:', data);
      // Full page reload to ensure templates, CSS, and data are fully refreshed
      location.reload();
    } catch (e) {
      statsBar.textContent = `Refresh failed: ${e.message}`;
      console.error('[Refresh] Error:', e);
    } finally {
      if (btn) { btn.disabled = false; if (svg) svg.classList.remove('spin'); }
      refreshing = false;
    }
  }

  function updateStats() {
    const selectedFolders = getSelectedFolders();
    let statsText = `Showing ${renderedCount} of ${currentList.length} models`;
    if (selectedFolders.length > 0) statsText += ` from ${selectedFolders.length} folder(s)`;
    if (allFolders.length > 0) statsText += ` (${allFolders.length} folders total)`;
    statsBar.textContent = statsText;
  }

  function sortModels(data, key, direction = 'desc') {
    return [...data].sort((a, b) => {
      let valA = a[key] || '';
      let valB = b[key] || '';
      if (key.includes('date')) { valA = new Date(valA); valB = new Date(valB); }
      const order = direction === 'asc' ? 1 : -1;
      return valA > valB ? order : valA < valB ? -order : 0;
    });
  }

  function applyControlsToModels() {
    const query = normalizeText(searchBox.value);
    const selectedFolders = getSelectedFolders();
    const terms = query.split(' ').filter(Boolean);

    let filtered = models.filter(entry => {
      const name = normalizeText(entry.name);
      const plainTextDesc = (entry.description || '').replace(/<[^>]+>/g, '').trim();
      const desc = normalizeText(plainTextDesc);
      const txt = (includeTxt && includeTxt.checked && entry.txt) ? normalizeText(entry.txt) : '';
      const folder = entry.folder_path || '';

      let matchesSearch = true;
      if (terms.length > 0) {
        if (partialMatch && partialMatch.checked) {
          // Each term must appear in at least one of the searched fields (partial match)
          matchesSearch = terms.every(term =>
            name.includes(term) || desc.includes(term) || (includeTxt && includeTxt.checked && txt.includes(term))
          );
        } else {
          // Whole-word match: each term must match as a whole token in at least one field
          matchesSearch = terms.every(term => {
            const rx = buildWordRegex(term);
            return rx.test(name) || rx.test(desc) || (includeTxt && includeTxt.checked && rx.test(txt));
          });
        }
      }

      const matchesFolder = selectedFolders.length === 0 || selectedFolders.includes(folder);
      return matchesSearch && matchesFolder;
    });
    return sortModels(filtered, sortSelect.value, sortDirection.value);
  }

  function rebuildList() {
    currentList = applyControlsToModels();
    resetAndRender();
  }

  function showPopup(entry) {
    const folderPath = entry.folder_path || '';
    const mediaFiles = (entry.video_image_files || '').split(',').map(f => f.trim()).filter(f => f);

    const mediaHtml = mediaFiles.map(f => {
      const fullPath = constructMediaPath(folderPath, f);
      const ext = f.split('.').pop().toLowerCase();
      if (['mp4', 'webm'].includes(ext)) {
        return `\n<video src="${fullPath}" muted loop controls preload="metadata" onmouseenter="this.play().catch(()=>{})" onmouseleave="this.pause();this.currentTime=0;" onerror="this.style.display='none'"></video>`;
      } else if (['jpg', 'jpeg', 'png', 'webp'].includes(ext)) {
        return `\n<img src="${fullPath}" alt="" loading="lazy" onerror="this.style.display='none'">`;
      }
      return '';
    }).filter(Boolean).join('');

    const urlBlock = entry.url ? `<p><strong>Source:</strong> <a href="${entry.url}" target="_blank">${escapeHtml(entry.url)}</a></p>` : '';
    const txtBlock = entry.txt ? `<h4>Additional Info:</h4><pre style="white-space:pre-wrap;background:#111;padding:1rem;border-radius:8px;color:#ccc;margin-bottom:1rem;">${escapeHtml(entry.txt)}</pre>` : '';
    const descBlock = entry.description ? `<h4>Description:</h4><div style="margin-bottom:1rem;">${entry.description}</div>` : '';

    const html = popupTemplate
      .replaceAll('{{NAME}}', escapeHtml(entry.name))
      .replaceAll('{{FOLDER}}', escapeHtml(folderPath || 'Unknown'))
      .replaceAll('{{MODEL}}', escapeHtml(entry.model))
      .replaceAll('{{URL_BLOCK}}', urlBlock)
      .replaceAll('{{TXT_BLOCK}}', txtBlock)
      .replaceAll('{{DESC_BLOCK}}', descBlock)
      .replaceAll('{{MEDIA_BLOCK}}', mediaHtml ? `<h4>All Media (${mediaFiles.length} files):</h4><div class="media-scroll">${mediaHtml}</div>` : '<p><em>No media files found</em></p>');

    popupBody.innerHTML = html;
    popup.style.display = 'flex';
  }

  function copyToClipboard(text, element) {
    navigator.clipboard.writeText(text).then(() => {
      const original = element.innerHTML;
      element.innerHTML = `✅ Copied!`;
      setTimeout(() => { element.innerHTML = original; }, 1000);
    });
  }

  function closePopup() {
    popup.style.display = 'none';
    popupBody.innerHTML = '';
  }

  // Expose for onclick handlers in templates
  window.showPopup = function(entry){ showPopup(entry); };
  window.copyToClipboard = function(text, el){ copyToClipboard(text, el); };
  window.closePopup = function(){ closePopup(); };

  // Event listeners
  searchBox.addEventListener('input', debounce(() => {
    const query = searchBox.value;
    localStorage.setItem('searchQuery', query);
    rebuildList();
  }, 200));

  partialMatch.addEventListener('change', () => {
    localStorage.setItem('partialMatch', partialMatch.checked ? '1' : '0');
    rebuildList();
  });

  includeTxt.addEventListener('change', () => {
    localStorage.setItem('includeTxt', includeTxt.checked ? '1' : '0');
    rebuildList();
  });

  folderChips.addEventListener('change', () => {
    const selected = getSelectedFolders();
    localStorage.setItem('selectedFolders', JSON.stringify(selected));
    rebuildList();
  });
  sortSelect.addEventListener('change', () => {
    localStorage.setItem('sortKey', sortSelect.value);
    rebuildList();
  });
  sortDirection.addEventListener('change', () => {
    localStorage.setItem('sortDirection', sortDirection.value);
    rebuildList();
  });

  popup.addEventListener('click', (e) => { if (e.target === popup) closePopup(); });

  // Delegate click for refresh button on cards
  galleryEl.addEventListener('click', (e) => {
    const btn = e.target && e.target.closest ? e.target.closest('.refresh-db') : null;
    if (btn) {
      e.preventDefault();
      refreshDatabase(btn);
    }
  });

  const io = new IntersectionObserver((entries) => {
    entries.forEach(entry => { if (entry.isIntersecting) renderNextBatch(); });
  });
  io.observe(sentinel);

  // Restore saved settings
  const savedQuery = localStorage.getItem('searchQuery') || '';
  const savedFolder = localStorage.getItem('selectedFolder') || '';
  const savedFolders = (() => {
    const raw = localStorage.getItem('selectedFolders');
    try { return raw ? JSON.parse(raw) : (savedFolder ? [savedFolder] : []); } catch { return []; }
  })();
  const savedSortKey = localStorage.getItem('sortKey') || 'created_date';
  const savedSortDir = localStorage.getItem('sortDirection') || 'desc';
  searchBox.value = savedQuery;
  // Defaults: whole words (partialMatch false), includeTxt false
  const savedPartial = localStorage.getItem('partialMatch') === '1';
  const savedIncludeTxt = localStorage.getItem('includeTxt') === '1';
  if (partialMatch) partialMatch.checked = !!savedPartial;
  if (includeTxt) includeTxt.checked = !!savedIncludeTxt;
  sortSelect.value = savedSortKey;
  sortDirection.value = savedSortDir;

  // Init
  Promise.all([
    fetchText('html/item.html').then(t => itemTemplate = t),
    fetchText('html/popup.html').then(t => popupTemplate = t),
    loadJSON('/api/models').then(data => { models = data || []; })
  ]).then(() => {
    if (models.length === 0) {
      statsBar.textContent = 'No models found. Run index_generate_model_db.py first.';
      return;
    }
    populateFolderFilter(models, savedFolders);
    currentList = applyControlsToModels();
    resetAndRender();
  }).catch(err => {
    statsBar.textContent = `Initialization error: ${err.message}`;
  });
})();
