#!/usr/bin/env python3
import os
import json
import sqlite3
from http.server import HTTPServer, SimpleHTTPRequestHandler
from urllib.parse import urlparse, parse_qs, unquote
import threading
import webbrowser
import time
import traceback
import importlib

# Import the generator utilities to refresh the DB
import index_generate_model_db as gen

PORT = 8081
DB_PATH = "./models.db"

class ModelGalleryHandler(SimpleHTTPRequestHandler):
    def do_OPTIONS(self):
        # Allow simple CORS preflight (mainly harmless for same-origin too)
        self.send_response(204)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()

    def do_GET(self):
        parsed_path = urlparse(self.path)
        
        # Debug logging
        print(f"🌐 Request: {self.path}")
        
        # API endpoint for models data
        if parsed_path.path == '/api/models':
            self.serve_models_json()
        # API endpoint for folders list
        elif parsed_path.path == '/api/folders':
            self.serve_folders_json()
        # Serve static files (HTML, CSS, JS, media)
        else:
            # Handle requests for media files in loras subfolders
            if self.path.startswith('/loras/'):
                # Decode URL and remove leading slash
                file_path = unquote(self.path[1:])  # Remove leading / and decode URL
                print(f"📁 Media request: {file_path}")
                print(f"📍 Decoded path: {file_path}")
                
                if os.path.exists(file_path):
                    print(f"✅ File exists: {file_path}")
                    self.serve_file(file_path)
                else:
                    print(f"❌ File not found: {file_path}")
                    print(f"📍 Current directory: {os.getcwd()}")
                    print(f"📂 Directory contents: {os.listdir('.')}")
                    if os.path.exists('loras'):
                        print(f"📂 Loras subdirs: {os.listdir('loras')}")
                        # Try to find similar files for debugging
                        for root, dirs, files in os.walk('loras'):
                            if any(f in file_path for f in files):
                                print(f"📂 Found similar files in {root}: {files}")
                    self.send_error(404, f"File not found: {file_path}")
            else:
                # Default behavior for other files
                super().do_GET()
    
    def do_POST(self):
        parsed_path = urlparse(self.path)
        print(f"🌐 POST: {self.path}")
        if parsed_path.path == '/api/refresh':
            self.handle_refresh()
        else:
            self.send_error(404, 'Not Found')
            return
    
    def serve_models_json(self):
        """Serve models data from SQLite database as JSON"""
        try:
            if not os.path.exists(DB_PATH):
                self.send_json_response([])
                return
            
            conn = sqlite3.connect(DB_PATH)
            conn.row_factory = sqlite3.Row  # This allows us to access columns by name
            cursor = conn.cursor()
            
            # Get query parameters
            parsed_path = urlparse(self.path)
            params = parse_qs(parsed_path.query)
            folder_filter = params.get('folder', [None])[0]
            
            # Build SQL query
            if folder_filter:
                cursor.execute("""
                    SELECT * FROM models 
                    WHERE folder_path = ?
                    ORDER BY created_date DESC
                """, (folder_filter,))
            else:
                cursor.execute("""
                    SELECT * FROM models 
                    ORDER BY created_date DESC
                """)
            
            # Convert rows to list of dictionaries
            models = []
            for row in cursor.fetchall():
                model_dict = dict(row)
                models.append(model_dict)
            
            conn.close()
            self.send_json_response(models)
            
        except sqlite3.Error as e:
            print(f"Database error: {e}")
            self.send_json_response([], error=f"Database error: {e}")
        except Exception as e:
            print(f"Error serving models: {e}")
            self.send_json_response([], error=f"Server error: {e}")
    
    def serve_folders_json(self):
        """Serve list of available folders"""
        try:
            if not os.path.exists(DB_PATH):
                self.send_json_response([])
                return
            
            conn = sqlite3.connect(DB_PATH)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT DISTINCT folder_path 
                FROM models 
                WHERE folder_path IS NOT NULL AND folder_path != ''
                ORDER BY folder_path
            """)
            
            folders = [row[0] for row in cursor.fetchall()]
            conn.close()
            
            self.send_json_response(folders)
            
        except Exception as e:
            print(f"Error serving folders: {e}")
            self.send_json_response([], error=f"Server error: {e}")

    def handle_refresh(self):
        try:
            start = time.time()
            # Always reload generator so refresh uses latest logic identical to batch run
            try:
                importlib.reload(gen)
                print("♻️ Reloaded index_generate_model_db module")
            except Exception as e:
                print(f"⚠️ Failed to reload generator module: {e}")

            # Parse JSON body if provided
            content_length = int(self.headers.get('Content-Length') or 0)
            body = self.rfile.read(content_length) if content_length > 0 else b''
            payload = {}
            if body:
                try:
                    payload = json.loads(body.decode('utf-8'))
                except Exception:
                    payload = {}
            print(f"🛠️ /api/refresh payload: {payload}")

            # Ensure DB/table exists
            gen.create_database()

            folder_path = (payload.get('folder_path') or '').strip() if isinstance(payload, dict) else ''
            model = (payload.get('model') or '').strip() if isinstance(payload, dict) else ''

            if folder_path and model:
                # Targeted single-item update
                print(f"🔧 Targeted update -> folder: '{folder_path}', model: '{model}'")
                written = gen.update_single_model(folder_path, model)
                duration = round(time.time() - start, 2)
                print(f"✅ Targeted update done. written={written} in {duration}s")
                self.send_json_response({
                    "ok": True,
                    "updated": written,
                    "duration_sec": duration,
                    "mode": "single"
                })
                return

            # Full refresh (process all models + cleanup of missing)
            print("🧹 Full refresh: cleanup + process all models")
            removed_count = gen.cleanup_database()
            model_data = gen.scan_all_folders()
            processed_count = gen.save_to_database(model_data)
            duration = round(time.time() - start, 2)
            print(f"✅ Full refresh done. processed={processed_count} removed={removed_count} in {duration}s")
            self.send_json_response({
                "ok": True,
                "processed": processed_count,
                "removed": removed_count,
                "duration_sec": duration,
                "mode": "full"
            })
        except Exception as e:
            print("Error during refresh:\n" + traceback.format_exc())
            self.send_response(500)
            self.send_header('Content-type', 'application/json; charset=utf-8')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            self.wfile.write(json.dumps({"ok": False, "error": str(e)}).encode('utf-8'))
    
    def send_json_response(self, data, error=None):
        """Send JSON response with proper headers"""
        json_string = json.dumps(data, indent=2, ensure_ascii=False)
        
        self.send_response(200)
        self.send_header('Content-type', 'application/json; charset=utf-8')
        self.send_header('Content-Length', len(json_string.encode('utf-8')))
        self.send_header('Access-Control-Allow-Origin', '*')  # Enable CORS if needed
        self.end_headers()
        
        self.wfile.write(json_string.encode('utf-8'))
    
    def serve_file(self, file_path):
        """Serve a file with proper MIME type and range support"""
        try:
            # Determine content type
            if file_path.lower().endswith(('.jpg', '.jpeg')):
                content_type = 'image/jpeg'
            elif file_path.lower().endswith('.png'):
                content_type = 'image/png'
            elif file_path.lower().endswith('.webp'):
                content_type = 'image/webp'
            elif file_path.lower().endswith('.mp4'):
                content_type = 'video/mp4'
            elif file_path.lower().endswith('.webm'):
                content_type = 'video/webm'
            else:
                content_type = 'application/octet-stream'
            
            # Get file size
            file_size = os.path.getsize(file_path)
            
            # Handle range requests for video files
            range_header = self.headers.get('Range')
            if range_header and content_type.startswith('video/'):
                # Parse range header
                range_start = 0
                range_end = file_size - 1
                
                if range_header.startswith('bytes='):
                    range_spec = range_header[6:]
                    if '-' in range_spec:
                        start_str, end_str = range_spec.split('-', 1)
                        if start_str:
                            range_start = int(start_str)
                        if end_str:
                            range_end = int(end_str)
                
                # Send partial content response
                self.send_response(206)  # Partial Content
                self.send_header('Content-type', content_type)
                self.send_header('Accept-Ranges', 'bytes')
                self.send_header('Content-Range', f'bytes {range_start}-{range_end}/{file_size}')
                self.send_header('Content-Length', range_end - range_start + 1)
                self.end_headers()
                
                # Send requested range
                with open(file_path, 'rb') as f:
                    f.seek(range_start)
                    chunk_size = min(8192, range_end - range_start + 1)
                    remaining = range_end - range_start + 1
                    
                    while remaining > 0:
                        chunk = f.read(min(chunk_size, remaining))
                        if not chunk:
                            break
                        self.wfile.write(chunk)
                        remaining -= len(chunk)
            else:
                # Send full file
                with open(file_path, 'rb') as f:
                    content = f.read()
                
                self.send_response(200)
                self.send_header('Content-type', content_type)
                self.send_header('Content-Length', len(content))
                self.send_header('Accept-Ranges', 'bytes')  # Support range requests
                self.end_headers()
                self.wfile.write(content)
            
        except Exception as e:
            print(f"Error serving file {file_path}: {e}")
            self.send_error(404, f"File not found: {file_path}")
    
    def log_message(self, format, *args):
        """Override to reduce log spam - only log errors"""
        if "404" in str(args) or "500" in str(args):
            super().log_message(format, *args)

def serve_folders_json(self):
    """Serve list of available folders"""
    try:
        if not os.path.exists(DB_PATH):
            self.send_json_response([])
            return
        
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT DISTINCT folder_path 
            FROM models 
            WHERE folder_path IS NOT NULL AND folder_path != ''
            ORDER BY folder_path
        """)
        
        folders = [row[0] for row in cursor.fetchall()]
        conn.close()
        
        self.send_json_response(folders)
        
    except Exception as e:
        print(f"Error serving folders: {e}")
        self.send_json_response([], error=f"Server error: {e}")
                    payload = json.loads(body.decode('utf-8'))
                except Exception:
                    payload = {}
            print(f"🛠️ /api/refresh payload: {payload}")

            # Ensure DB/table exists
            gen.create_database()

            folder_path = (payload.get('folder_path') or '').strip() if isinstance(payload, dict) else ''
            model = (payload.get('model') or '').strip() if isinstance(payload, dict) else ''

            if folder_path and model:
                # Targeted single-item update
                print(f"🔧 Targeted update -> folder: '{folder_path}', model: '{model}'")
                written = gen.update_single_model(folder_path, model)
                duration = round(time.time() - start, 2)
                print(f"✅ Targeted update done. written={written} in {duration}s")
                self.send_json_response({
                    "ok": True,
                    "updated": written,
                    "duration_sec": duration,
                    "mode": "single"
                })
                return

            # Full refresh (process all models + cleanup of missing)
            print("🧹 Full refresh: cleanup + process all models")
            removed_count = gen.cleanup_database()
            model_data = gen.scan_all_folders()
            processed_count = gen.save_to_database(model_data)
            duration = round(time.time() - start, 2)
            print(f"✅ Full refresh done. processed={processed_count} removed={removed_count} in {duration}s")
            self.send_json_response({
                "ok": True,
                "processed": processed_count,
                "removed": removed_count,
                "duration_sec": duration,
                "mode": "full"
            })
        except Exception as e:
            print("Error during refresh:\n" + traceback.format_exc())
            self.send_response(500)
            self.send_header('Content-type', 'application/json; charset=utf-8')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            self.wfile.write(json.dumps({"ok": False, "error": str(e)}).encode('utf-8'))

def open_browser():
    """Open browser after a short delay"""
    time.sleep(1.5)
    webbrowser.open(f'http://localhost:{PORT}')

def main():
    # Set working directory to script location
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    # Check if database exists
    if not os.path.exists(DB_PATH):
        print(f"⚠️  Warning: Database not found at {DB_PATH}")
        print("   Run 'python index_generate_model_db.py' first to create the database.")
        print()
    
    # Check if HTML file exists
    if not os.path.exists('index.html'):
        print(f"⚠️  Warning: index.html not found in {script_dir}")
        print()
    
    # Start server
    server_address = ('', PORT)
    httpd = HTTPServer(server_address, ModelGalleryHandler)
    
    print(f"🚀 LoRA Model Gallery Server")
    print(f"   📁 Serving from: {script_dir}")
    print(f"   🗄️  Database: {DB_PATH}")
    print(f"   🌐 URL: http://localhost:{PORT}")
    print(f"   ⏹️  Press Ctrl+C to stop")
    print()
    
    # Open browser in a separate thread
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True  # Dies when main thread dies
    browser_thread.start()
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print(f"\n👋 Server stopped")
        httpd.shutdown()

if __name__ == "__main__":
    main()