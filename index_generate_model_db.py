import os
import json
import re
import sqlite3
from bs4 import BeautifulSoup
from datetime import datetime
import html as html_lib

# === SET YOUR TARGET FOLDER HERE ===
LORAS_FOLDER = "./loras"  # Main loras folder containing subdirectories
DB_PATH = "./models.db"   # SQLite database path

def create_database():
    """Create SQLite database with models table if it doesn't exist"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS models (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            description TEXT,
            video_image_files TEXT,
            model TEXT NOT NULL,
            folder_path TEXT NOT NULL,
            created_date TEXT,
            added_date TEXT,
            url TEXT,
            txt TEXT,
            UNIQUE(model, folder_path)
        )
    ''')
    
    conn.commit()
    conn.close()

def get_existing_models():
    """Get existing models as a set of (model, folder_path) for quick membership checks."""
    if not os.path.exists(DB_PATH):
        return set()
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    try:
        cursor.execute("SELECT model, folder_path FROM models")
        existing = {(row[0], row[1]) for row in cursor.fetchall()}
    except sqlite3.OperationalError:
        existing = set()
    conn.close()
    return existing

def get_existing_rows_map():
    """Return a dict keyed by (model, folder_path) -> row dict with existing DB values."""
    rows = {}
    if not os.path.exists(DB_PATH):
        return rows
    conn = sqlite3.connect(DB_PATH)
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    try:
        cursor.execute("SELECT * FROM models")
        for r in cursor.fetchall():
            key = (r["model"], r["folder_path"])
            rows[key] = dict(r)
    except sqlite3.OperationalError:
        pass
    finally:
        conn.close()
    return rows

def extract_model_data_from_folder(folder_path, existing_rows_map, target_model: str | None = None):
    """Extract model data from a specific folder"""
    model_data_list = []
    
    if not os.path.exists(folder_path):
        print(f"⚠️ Folder not found: {folder_path}")
        return model_data_list
    
    all_files = os.listdir(folder_path)
    safetensor_files = [f for f in all_files if f.endswith(".safetensors")]
    
    if not safetensor_files:
        print(f"  📁 No .safetensors files found in: {folder_path}")
        return model_data_list
    
    folder_name = os.path.basename(folder_path)
    relative_folder = os.path.relpath(folder_path, LORAS_FOLDER)
    
    total = len(safetensor_files)
    print(f"  📁 Processing folder: {folder_name} ({total} models)")
    
    for idx, file in enumerate(safetensor_files, start=1):
        # If a specific model is targeted, only process that file
        if target_model and file != target_model:
            continue
        print(f"    [{idx}/{total}] Processing: {file}")
        
        # When target_model is specified, always process that specific model
        # When target_model is None (batch mode), also process all models to ensure updates
        # This ensures both batch execution and refresh button work identically

        base_name = os.path.splitext(file)[0]
        html_file = os.path.join(folder_path, f"{base_name}.html")
        json_file = os.path.join(folder_path, f"{base_name}.json")
        url_file = os.path.join(folder_path, f"{base_name}.url")
        model_file_path = os.path.join(folder_path, file)
        if target_model:
            print(f"      📄 Model path: {model_file_path} exists={os.path.exists(model_file_path)}")
        url = None
        
        # Extract name and description from HTML if available
        name = base_name
        description_html = ""
        json_desc_text = ""
        json_activation_text = ""
        json_notes_text = ""
        
        if os.path.exists(html_file):
            try:
                with open(html_file, "r", encoding="utf-8") as f:
                    soup = BeautifulSoup(f, "html.parser")
                
                # Extract name
                title_tag = soup.select_one("h1.mantine-Title-root")
                if title_tag:
                    name = title_tag.get_text(strip=True)
                
                # Extract description HTML - prioritize main section short description
                desc_block = None
                description_html = ""

                # Narrow scope to main section when present
                scope = soup
                main_section = soup.select_one("div[class*='ModelVersionDetails_mainSection']")
                if main_section:
                    scope = main_section

                # 1) EARLY: pick the first element whose text starts with 'Place it'
                early_candidates = scope.select("div.mantine-TypographyStylesProvider-root")
                picked_early = False
                for el in early_candidates:
                    text = el.get_text(" ", strip=True)
                    if re.search(r"^\s*Place it\b", text, re.IGNORECASE):
                        desc_block = el
                        description_html = str(desc_block)
                        picked_early = True
                        print("      ✅ Picked description by early 'Place it' match")
                        break

                # 2) If no 'Place it' block, pick first main-section block that is NOT a comment/small block
                if desc_block is None and not picked_early:
                    for el in scope.select("div.mantine-TypographyStylesProvider-root"):
                        cls = " ".join(el.get("class", []))
                        text = el.get_text(" ", strip=True)
                        # Exclude small/comment-like blocks
                        if ("text-sm" in cls) or ("http://" in text or "https://" in text):
                            continue
                        # Prefer reasonably short lead paragraph
                        if len(text) > 400:
                            continue
                        desc_block = el
                        description_html = str(desc_block)
                        print("      ✅ Picked first main-section paragraph (non-comment)")
                        break

                if desc_block is None and not picked_early:
                    # Collect candidates in priority order
                    candidates = []
                    candidates.extend(scope.select("div.mantine-TypographyStylesProvider-root"))
                    if not candidates:
                        candidates.extend(scope.select("div.mantine-Spoiler-content"))
                    if not candidates:
                        candidates.extend(soup.select("div.mantine-TypographyStylesProvider-root, div.mantine-Spoiler-content"))

                    def score_candidate(el):
                        text = el.get_text(" ", strip=True)
                        length = len(text)
                        cls = " ".join(el.get("class", []))
                        score = 0
                        # Prefer elements mentioning 'place it'
                        if re.search(r"\bplace it\b", text, re.IGNORECASE):
                            score += 200
                        # Prefer within main section
                        if scope is main_section:
                            score += 100
                        # Prefer moderate/short content to avoid giant comment blocks
                        score += max(0, 500 - min(length, 500))  # shorter => higher
                        # Penalize likely comment blocks
                        if "text-sm" in cls or "RenderHtml_htmlRenderer" in cls:
                            score -= 150
                        # Light penalty if extremely short
                        if length < 15:
                            score -= 30
                        return score, length

                    if candidates and desc_block is None:
                        scored = [(score_candidate(el), el) for el in candidates]
                        # Sort by score desc, then by length desc (as tie-breaker to keep some substance)
                        scored.sort(key=lambda x: (x[0][0], x[0][1]), reverse=True)
                        best = scored[0][1]
                        desc_block = best
                        description_html = str(desc_block)
                        print(f"      ✅ Picked description (score={scored[0][0][0]}, len={scored[0][0][1]})")
                    else:
                        print("      ❌ No description elements found in expected selectors")

                # Debug chosen text snippet
                if description_html:
                    try:
                        chosen_text = BeautifulSoup(description_html, "html.parser").get_text(" ", strip=True)
                        print("      🔎 Chosen description text:", chosen_text[:120])
                    except Exception:
                        pass
            except Exception as e:
                print(f"      ⚠️ Error reading HTML file: {e}")
        else:
            print(f"      ℹ️ No HTML file found, using filename as name")

        # Read JSON metadata if present and map fields
        if os.path.exists(json_file):
            try:
                with open(json_file, "r", encoding="utf-8") as f:
                    data = json.load(f)

                # Support case-insensitive top-level keys
                if isinstance(data, dict):
                    lowered = {str(k).lower(): v for k, v in data.items()}
                    json_desc_text = str(lowered.get("description", "")).strip()
                    json_activation_text = str(lowered.get("activation text", "")).strip()
                    if not json_activation_text:
                        json_activation_text = str(lowered.get("activation_text", "")).strip()
                    json_notes_text = str(lowered.get("notes", "")).strip()

                    # If description starts with a URL, extract it into url field
                    if json_desc_text:
                        m = re.match(r"^\s*(https?://\S+)", json_desc_text, re.IGNORECASE)
                        if m:
                            extracted_url = m.group(1)
                            # Prefer existing URL file, but if not present, use JSON-derived URL
                            if not 'url' in locals() or url is None:
                                url = extracted_url
                else:
                    print(f"      ⚠️ JSON metadata for {base_name} is not an object; skipping key mapping")
            except Exception as e:
                print(f"      ⚠️ Error reading JSON file: {e}")

        # Gather related media files
        pattern = re.compile(rf"{re.escape(base_name)}(-\d+)?\.(mp4|webp|jpg|jpeg|png)$", re.IGNORECASE)
        media_matches = [f for f in all_files if pattern.match(f)]
        
        # Move model-matching file to front (e.g., model_name.mp4 comes before model_name-1.mp4)
        media_files = sorted(media_matches, key=lambda f: 0 if os.path.splitext(f)[0].lower() == base_name.lower() else 1)
        
        # Look for any preview files if no specific matches found
        if not media_files:
            preview_files = [f for f in all_files if f.lower().startswith('preview.') and 
                           f.lower().endswith(('.mp4', '.webp', '.jpg', '.jpeg', '.png'))]
            media_files.extend(preview_files)
        
        # Debug output for media files
        if media_files:
            print(f"      🎬 Found {len(media_files)} media files: {', '.join(media_files)}")
        else:
            print(f"      📷 No media files found for {base_name}")
        
        # Dates
        created_ts = os.path.getctime(model_file_path)
        created_date = datetime.fromtimestamp(created_ts).isoformat()
        # Preserve added_date if row exists
        existing_row = existing_rows_map.get((file, relative_folder))
        added_date = existing_row.get("added_date") if existing_row else datetime.now().isoformat()
        
        # Extract URL
        if os.path.exists(url_file):
            try:
                with open(url_file, "r", encoding="utf-8") as f:
                    for line in f:
                        if line.startswith("URL="):
                            url = line.strip().split("=", 1)[-1]
                            break
            except Exception as e:
                print(f"      ⚠️ Error reading URL file: {e}")
        
        # Optional .txt file (model-specific)
        txt_file = os.path.join(folder_path, f"{base_name}.txt")
        txt_content = ""
        if os.path.exists(txt_file):
            try:
                with open(txt_file, "r", encoding="utf-8") as f:
                    txt_content = f.read().strip()
            except Exception as e:
                print(f"      ⚠️ Error reading TXT file: {e}")
        else:
            if target_model:
                print(f"      🗒️ Model-specific TXT missing: {txt_file}")
        
        # Do NOT aggregate other folder-level txt files. Only use model-specific txt as requested.

        # Merge JSON 'activation text' + 'notes' into txt
        json_txt_parts = []
        if json_activation_text:
            json_txt_parts.append(json_activation_text)
        if json_notes_text:
            json_txt_parts.append(json_notes_text)
        if json_txt_parts:
            json_txt_combined = "\n\n".join([p for p in json_txt_parts if p])
            if txt_content:
                # Append JSON-derived text after existing text
                txt_content = f"{txt_content}\n\n{json_txt_combined}"
            else:
                txt_content = json_txt_combined

        # Merge description: HTML first, then JSON description text
        if json_desc_text:
            escaped_json_desc = html_lib.escape(json_desc_text).replace("\n", "<br/>")
            json_desc_html = f"<div class=\"json-description\"><p>{escaped_json_desc}</p></div>"
            if description_html:
                description_html = f"{description_html}\n<hr/>\n{json_desc_html}"
            else:
                description_html = json_desc_html
        
        model_data = {
            "name": name,
            "description": description_html,
            "video_image_files": ", ".join(media_files),
            "model": file,
            "folder_path": relative_folder,
            "created_date": created_date,
            "added_date": added_date,
            "url": url,
            "txt": txt_content
        }
        if target_model:
            print(f"      ✅ Final media list count={len(media_files)} -> {', '.join(media_files)}")
            print(f"      ✅ Final txt length={len(txt_content or '')}")
        
        model_data_list.append(model_data)
    
    return model_data_list

def scan_all_folders():
    """Scan all subfolders in the loras directory and process all models (update existing, add new)."""
    if not os.path.exists(LORAS_FOLDER):
        print(f"❌ Main loras folder not found: {LORAS_FOLDER}")
        return []
    
    all_model_data = []
    existing_rows = get_existing_rows_map()
    
    # Get all subdirectories in loras folder
    subdirs = []
    for item in os.listdir(LORAS_FOLDER):
        item_path = os.path.join(LORAS_FOLDER, item)
        if os.path.isdir(item_path):
            subdirs.append(item_path)
    
    if not subdirs:
        print(f"⚠️ No subdirectories found in: {LORAS_FOLDER}")
        return []
    
    print(f"🔍 Found {len(subdirs)} subdirectories to scan:")
    for subdir in subdirs:
        print(f"  📁 {os.path.basename(subdir)}")
    
    print(f"\n🚀 Starting scan...")
    
    for folder_path in subdirs:
        folder_data = extract_model_data_from_folder(folder_path, existing_rows, target_model=None)
        all_model_data.extend(folder_data)
    
    return all_model_data

def update_single_model(relative_folder: str, model_file: str) -> int:
    """Update or insert a single model row identified by its relative folder and model filename.
    Returns number of rows written (0 or 1).
    """
    folder_path = os.path.join(LORAS_FOLDER, relative_folder)
    if not os.path.isdir(folder_path):
        print(f"❌ Folder not found: {folder_path}")
        return 0
    existing_rows = get_existing_rows_map()
    model_data_list = extract_model_data_from_folder(folder_path, existing_rows, target_model=model_file)
    return save_to_database(model_data_list)

def save_to_database(model_data_list):
    """Save model data to SQLite database"""
    if not model_data_list:
        print("  ℹ️ No new models to add")
        return 0
    
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    added_count = 0
    for model_data in model_data_list:
        try:
            cursor.execute('''
                INSERT OR REPLACE INTO models 
                (name, description, video_image_files, model, folder_path, 
                 created_date, added_date, url, txt)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                model_data["name"],
                model_data["description"],
                model_data["video_image_files"],
                model_data["model"],
                model_data["folder_path"],
                model_data["created_date"],
                model_data["added_date"],
                model_data["url"],
                model_data["txt"]
            ))
            added_count += 1
        except sqlite3.Error as e:
            print(f"    ❌ Error adding model {model_data['model']}: {e}")
    
    conn.commit()
    conn.close()
    
    return added_count

def cleanup_database():
    """Remove entries for models that no longer exist on disk"""
    if not os.path.exists(DB_PATH):
        return 0
    
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    try:
        cursor.execute("SELECT id, model, folder_path FROM models")
        all_entries = cursor.fetchall()
    except sqlite3.OperationalError:
        conn.close()
        return 0
    
    removed_count = 0
    for entry_id, model_file, folder_path in all_entries:
        full_path = os.path.join(LORAS_FOLDER, folder_path, model_file)
        if not os.path.exists(full_path):
            cursor.execute("DELETE FROM models WHERE id = ?", (entry_id,))
            removed_count += 1
            print(f"  🗑️ Removed: {folder_path}/{model_file} (file not found)")
    
    conn.commit()
    conn.close()
    
    return removed_count

if __name__ == "__main__":
    print("🎯 LoRA Model Database Generator - Multi-folder Support")
    print("=" * 60)
    
    # Create database if it doesn't exist
    create_database()
    
    # Clean up database (remove entries for deleted files)
    print("🧹 Cleaning up database...")
    removed_count = cleanup_database()
    
    # Scan all folders and process all model data (update existing, add new)
    print("\n🔍 Scanning and processing all models...")
    model_data = scan_all_folders()

    # Save to database
    print(f"\n💾 Saving to database...")
    processed_count = save_to_database(model_data)

    # Summary
    print(f"\n✅ Database updated: {DB_PATH}")
    print(f"   📝 {processed_count} models processed (added/updated)")
    print(f"   ➖ {removed_count} old models removed (missing files)")
    print(f"   📁 Scanned folders: {LORAS_FOLDER}")

    if processed_count == 0 and removed_count == 0:
        print("   ℹ️ No changes needed - database is up to date!")