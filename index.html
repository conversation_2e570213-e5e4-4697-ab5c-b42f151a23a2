<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Model Gallery</title>
  <link rel="stylesheet" href="css/styles.css">
</head>
<body>
  <h1>LoRA Model Gallery</h1>
  <div id="controls">
    <div class="controls-row">
      <input type="text" id="searchBox" placeholder="Search by name or description...">
      <label class="chip" title="Toggle partial word matching (off = whole words)">
        <input type="checkbox" id="partialMatch">
        <span>Partial words</span>
      </label>
      <label class="chip" title="Include 'txt' field in search">
        <input type="checkbox" id="includeTxt">
        <span>Search in txt</span>
      </label>
    </div>
    <div class="controls-row">
      <div id="folderChips" class="folder-chips" title="Select one or more folders (leave empty for all)"></div>
    </div>
    <div class="controls-row">
      <select id="sortSelect">
        <option value="created_date">Created Date</option>
        <option value="added_date">Sort by: Added Date</option>
        <option value="name">Name</option>
        <option value="model">Model File</option>
      </select>
      <select id="sortDirection">
        <option value="desc">⬇️ DESC</option>
        <option value="asc">⬆️ ASC</option>
      </select>
    </div>
  </div>
  <div class="stats-bar" id="statsBar">Loading...</div>
  <div class="grid" id="gallery"></div>
  <!-- Sentinel for infinite scroll -->
  <div id="sentinel" style="height: 1px"></div>
  
  <!-- Popup -->
  <div class="popup" id="popup">
    <div class="popup-content" id="popupContent">
      <div id="popupBody"></div>
    </div>
  </div>
  
  <script src="js/main.js"></script>
</body>
</html>